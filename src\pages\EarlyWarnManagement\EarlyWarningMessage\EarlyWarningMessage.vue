<!--预警消息-->
<template>
  <div>
    <el-form :inline="true" :model="search" ref="searchFormRef">
      <el-form-item label="预警名称：" prop="query">
        <el-input v-model="search.query" placeholder="请输入预警名称" maxlength="100" />
      </el-form-item>
      <el-form-item label="上报时间：" prop="reportTimeRange">
        <el-date-picker
          v-model="search.reportTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="第三方名称：" prop="thirdPartyName">
        <el-input v-model="search.thirdPartyName" placeholder="请输入第三方名称" maxlength="100" />
      </el-form-item>
      <el-form-item label="业务类型：" prop="businessType">
        <el-select v-model="search.businessType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in store().businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="系统名称：" prop="systemName">
        <el-select v-model="search.systemName" filterable allow-create default-first-option placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in store().systemNameOptions"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警类型：" prop="alertType">
        <el-select v-model="search.alertType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in store().alertTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警级别：" prop="alertLevel">
        <el-select v-model="search.alertLevel" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in store().warnLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="模型编号：" prop="modelNumber">
        <el-input v-model="search.modelNumber" placeholder="请输入预警模型编号" maxlength="100" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button @click="resetForm(searchFormRef)">重置</el-button>
        <el-button @click="handleExport">下载</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      border
      stripe
      v-loading="loading"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
      @selection-change="handleSelectionChange"
    >
      <template #empty>
        <div class="empty-data">
          <span>暂无数据</span>
        </div>
      </template>
      <el-table-column type="selection" width="55" />
      <el-table-column prop="modelName" label="预警名称" min-width="120">
        <template #default="{ row }">
          <el-tooltip
            v-if="row.modelName && row.modelName.length > 10"
            class="text-tooltip"
            placement="top"
            :show-after="300"
            popper-class="custom-tooltip">
            <template #content>
              <div class="tooltip-content">{{ row.modelName }}</div>
            </template>
            <div class="ellipsis">{{ row.modelName }}</div>
          </el-tooltip>
          <span v-else>{{ row.modelName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="modelCode" label="模型编号" min-width="100" />
      <el-table-column prop="alertType" label="预警类型" min-width="100">
        <template #default="{ row }">
          {{ getAlertTypeLabel(row.alertType) }}
        </template>
      </el-table-column>
      <el-table-column prop="platformName" label="第三方名称" min-width="120">
        <template #default="{ row }">
          <el-tooltip
            v-if="row.platformName && row.platformName.length > 10"
            class="text-tooltip"
            placement="top"
            :show-after="300"
            popper-class="custom-tooltip">
            <template #content>
              <div class="tooltip-content">{{ row.platformName }}</div>
            </template>
            <div class="ellipsis">{{ row.platformName }}</div>
          </el-tooltip>
          <span v-else>{{ row.platformName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="businessType" label="业务类型" min-width="100">
        <template #default="{ row }">
          {{ getBusinessTypeLabel(row.businessType) }}
        </template>
      </el-table-column>
      <el-table-column prop="warnLevel" label="预警级别" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getWarningLevelType(row.warnLevel)">
            {{ getWarningLevelLabel(row.warnLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="ruleName" label="预警规则" min-width="120" align="center">
        <template #default="{ row }">
          <el-tooltip
            v-if="row.ruleName && row.ruleName.length > 10"
            class="text-tooltip"
            placement="top"
            :show-after="300"
            popper-class="custom-tooltip">
            <template #content>
              <div class="tooltip-content">{{ row.ruleName }}</div>
            </template>
            <div class="ellipsis">{{ row.ruleName }}</div>
          </el-tooltip>
          <span v-else>{{ row.ruleName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="systemName" label="系统名称" min-width="120">
        <template #default="{ row }">
          <el-tooltip
            v-if="row.systemName && row.systemName.length > 10"
            class="text-tooltip"
            placement="top"
            :show-after="300"
            popper-class="custom-tooltip">
            <template #content>
              <div class="tooltip-content">{{ row.systemName }}</div>
            </template>
            <div class="ellipsis">{{ row.systemName }}</div>
          </el-tooltip>
          <span v-else>{{ row.systemName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="payload" label="拓展" min-width="200">
        <template #default="{ row }">
          <el-tooltip
            v-if="row.payload && row.payload !== '-'"
            class="payload-tooltip"
            placement="top"
            :show-after="300"
            popper-class="custom-payload-tooltip">
            <template #content>
              <div class="payload-tooltip-content">{{ row.payload }}</div>
            </template>
            <div class="ellipsis">{{ row.payload }}</div>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="alertTime" label="上报时间" min-width="160" />
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" text @click="handleView(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="mt20 flex_row flex_center">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :background="true"
        layout="prev, pager, next, jumper, sizes, total"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 查看详情对话框 -->
    <MessageDetailDialog
      v-model:dialogVisible="detailDialog.show"
      :detailData="detailDialog.detailData"
    />

    <!-- 处理对话框 -->
    <!-- <ProcessDialog
      v-model:dialogVisible="processDialog.show"
      :dialogForm="processDialog.form"
      @callback="getList"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { store } from '@/stores'
import EarlyWarnManageApi from '@/api/EarlyWarnManage'
import MessageDetailDialog from './components/MessageDetailDialog.vue'
import http from '@/assets/js/http'
import { sysApi } from '@/globalSettings'

import type { FormInstance } from 'element-plus'
import type {
  AlertMessageQueryParams,
  AlertMessagePageVO,
  AlertMessageDetailVO,
  AlertMessageExportBO,
  PageResponse,
  ApiResponse,
  SearchForm
} from '@/types/alertMessage'

// 获取当天的时间范围
const getTodayTimeRange = (): string[] => {
  const today = new Date()
  const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
  const endTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

  const formatDateTime = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  return [formatDateTime(startTime), formatDateTime(endTime)]
}

// 搜索表单
const search = reactive<SearchForm>({
  query: '',
  reportTimeRange: getTodayTimeRange(), // 默认设置为当天的时间范围
  thirdPartyName: '',
  businessType: '', // 修改为空字符串，对应"全部"选项的value
  systemName: '', // 修改为空字符串，对应"全部"选项的value
  alertType: '', // 修改为空字符串，对应"全部"选项的value
  alertLevel: '', // 修改为空字符串，对应"全部"选项的value
  modelNumber: ''
})

// 根据业务类型值获取显示文本
const getBusinessTypeLabel = (value: string): string => {
  const option = store().businessTypeOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 根据预警类型值获取显示文本
const getAlertTypeLabel = (value: string): string => {
  const option = store().alertTypeOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 根据预警级别值获取显示文本
const getWarningLevelLabel = (value: string): string => {
  const option = store().warnLevelOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 预警级别类型映射
const getWarningLevelType = (level: string): 'danger' | 'warning' | 'success' | 'info' => {
  // 获取显示文本
  const label = getWarningLevelLabel(level)

  // 根据显示文本或值进行类型映射
  const typeMap: Record<string, 'danger' | 'warning' | 'success' | 'info'> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success',
    'high': 'danger',
    'medium': 'warning',
    'low': 'success'
  }
  return typeMap[label] || typeMap[level] || 'info'
}



// 表格数据
const tableData = ref<AlertMessagePageVO[]>([])

const loading = ref(false)

// 表格选择
const selectedIds = ref<number[]>([])
const handleSelectionChange = (selection: AlertMessagePageVO[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const searchFormRef = ref<FormInstance>()

// 详情对话框状态
const detailDialog = reactive({
  show: false,
  detailData: {} as AlertMessageDetailVO
})

// 构建查询参数
const buildQueryParams = (page = 1): AlertMessageQueryParams => {
  const params: AlertMessageQueryParams = {
    pageNum: page,
    pageSize: pagination.pageSize
  }

  // 预警名称模糊查询
  if (search.query.trim()) {
    params.modelName = search.query.trim()
  }

  // 第三方名称
  if (search.thirdPartyName.trim()) {
    params.platformName = search.thirdPartyName.trim()
  }

  // 业务类型
  if (search.businessType && search.businessType !== '') {
    params.businessType = search.businessType
  }

  // 系统名称
  if (search.systemName && search.systemName !== '') {
    params.systemName = search.systemName
  }

  // 预警类型
  if (search.alertType && search.alertType !== '') {
    params.alertType = search.alertType
  }

  // 预警级别
  if (search.alertLevel && search.alertLevel !== '') {
    params.warnLevel = search.alertLevel
  }

  // 模型编号
  if (search.modelNumber.trim()) {
    params.modelCode = search.modelNumber.trim()
  }

  // 上报时间范围
  if (search.reportTimeRange && search.reportTimeRange.length === 2) {
    params.alertTimeStart = search.reportTimeRange[0]
    params.alertTimeEnd = search.reportTimeRange[1]
  }

  return params
}

// 获取列表数据
const getList = async (page = 1) => {
  try {
    loading.value = true

    const params = buildQueryParams(page)
    const response: ApiResponse<PageResponse<AlertMessagePageVO>> = await EarlyWarnManageApi.getAlertMessage(params)

    if (response.code === '0000') {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
      pagination.currentPage = response.data.current || page
      pagination.pageSize = response.data.size || 10
    } else {
      ElMessage.error(response.message || '查询失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取预警消息列表失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = (formEl: any) => {
  if (!formEl) return
  formEl.resetFields()
  Object.assign(search, {
    query: '',
    reportTimeRange: getTodayTimeRange(), // 重置时也使用当天的时间范围
    thirdPartyName: '',
    businessType: '', // 修改为空字符串，对应"全部"选项的value
    systemName: '', // 修改为空字符串，对应"全部"选项的value
    alertType: '', // 修改为空字符串，对应"全部"选项的value
    alertLevel: '', // 修改为空字符串，对应"全部"选项的value
    modelNumber: ''
  })
  getList(1)
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getList(1)
}

const handleCurrentChange = (val: number) => {
  getList(val)
}



// 查看详情
const handleView = async (row: AlertMessagePageVO) => {
  try {
    const response: ApiResponse<AlertMessageDetailVO> = await EarlyWarnManageApi.getAlertMessageDetail(row.id)

    if (response.code === '0000') {
      detailDialog.detailData = response.data
      detailDialog.show = true
    } else {
      ElMessage.error(response.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取预警消息详情失败:', error)
    ElMessage.error('获取详情失败，请稍后重试')
  }
}



// 导出数据
const handleExport = async () => {
  try {
    // 检查是否有选中的记录
    if (selectedIds.value.length === 0) {
      ElMessage.warning('请勾选需要导出的数据')
      return
    }

    // 构建导出参数，使用和查询一样的排序规则
    const exportParams: AlertMessageExportBO = {
      ids: selectedIds.value
    }

    // 使用 fileBlob 方法下载文件
    await http.fileBlob(
      `${sysApi}/alertMessage/export`,
      exportParams,
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      true,
      'POST'
    )

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 初始化数据
onMounted(async () => {
  await store().allSystemName()
  await store().allBusinessType()
  await store().allAlertType()
  await store().allWarnLevel()
  getList(1)
})
</script>

<style lang="scss" scoped>
.el-input {
  width: 220px;
}
.el-select {
  width: 220px;
}
.el-message-box__container {
  align-items: normal;
}
.ellipsis {
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center !important;
  margin: 0 auto;
}

/* 强制预警规则列居中显示 - 使用多种选择器确保生效 */
:deep(.el-table__body-wrapper .el-table__body .el-table__row .el-table__cell:nth-child(8)) {
  text-align: center !important;
}

:deep(.el-table__body-wrapper .el-table__body .el-table__row .el-table__cell:nth-child(8) .cell) {
  text-align: center !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.el-table__body-wrapper .el-table__body .el-table__row .el-table__cell:nth-child(8) .cell span) {
  text-align: center !important;
  width: 100% !important;
}

/* 备用方案：通过类名选择器 */
:deep(.text-tooltip) {
  width: 100% !important;
  text-align: center !important;
  display: flex !important;
  justify-content: center !important;
}

:deep(.text-tooltip .ellipsis) {
  text-align: center !important;
  margin: 0 auto !important;
}

// 全局tooltip样式
:deep(.custom-tooltip) {
  max-width: 50vw !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  white-space: normal !important;
  line-height: 1.4 !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
}

:deep(.custom-payload-tooltip) {
  max-width: 50vw !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  white-space: pre-wrap !important;
  line-height: 1.5 !important;
  padding: 12px !important;
  font-size: 13px !important;
}

.tooltip-content {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}

.payload-tooltip-content {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
}

:deep(.box-item) {
  width: 600px;
  font-size: 29px;
}

.empty-data {
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
}
</style>
