<template>
  <el-dialog
    title="通讯录"
    v-model="visible"
    width="800px"
    :before-close="handleClose"
  >
    <div class="contact-selector">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索"
          prefix-icon="Search"
          clearable
          @input="handleSearch"
          @close="handleClose"
        />
      </div>

      <!-- 导航路径 -->
      <div class="navigation-path">
        <el-button type="text" @click="goBack" :disabled="!canGoBack">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <span class="current-path">{{ currentPath }}</span>
      </div>

      <div class="selector-content">
        <!-- 左侧组织架构 -->
        <div class="organization-tree">
          <div class="tree-header">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
              :indeterminate="isIndeterminate"
            >
              全选
            </el-checkbox>
            <span v-if="searchKeyword" class="search-status">
              搜索: "{{ searchKeyword }}" ({{ getUsers(treeData).length }}个结果)
            </span>
          </div>
          <div class="tree-container">
            <el-tree
              ref="treeRef"
              :data="treeData"
              :props="treeProps"
              node-key="id"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
              :default-expanded-keys="expandedKeys"
            >
              <template #default="{ node, data }">
                <div class="tree-node">
                  <el-checkbox
                    v-model="data.checked"
                    @change="handleItemSelect(data, $event)"
                  />
                  <span 
                    class="node-label" 
                    v-html="searchKeyword ? highlightKeyword(data.name, searchKeyword) : data.name"
                  ></span>
                  <el-avatar 
                    v-if="data.avatar" 
                    :size="24" 
                    :src="data.avatar"
                    class="user-avatar"
                  />
                </div>
              </template>
            </el-tree>
          </div>
        </div>

        <!-- 右侧已选人员 -->
        <div class="selected-contacts">
          <div class="selected-header">
            <span>已选择 ({{ selectedContacts.length }}/1000)</span>
          </div>
          <div class="selected-list">
            <el-tag
              v-for="contact in selectedContacts"
              :key="contact.id"
              closable
              @close="removeContact(contact)"
              class="contact-tag"
            >
              {{ contact.name }}
            </el-tag>
            <div v-if="selectedContacts.length === 0" class="empty-tip">
              暂无选择的人员
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import OrganizationApi from '@/api/Organization'
import type { OrganizationTreeVO } from '@/types/organization'


interface Contact {
  id: string
  name: string
  avatar?: string
  type: 'USER' | 'DEPT'
  checked?: boolean
  children?: Contact[]
}

interface Props {
  modelValue: boolean
  selectedContacts?: Contact[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', contacts: Contact[]): void
}

const props = withDefaults(defineProps<Props>(), {
  selectedContacts: () => []
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const searchKeyword = ref('')
const currentPath = ref('组织架构')
const expandedKeys = ref<string[]>([])
const treeRef = ref()
const navigationHistory = ref<{id: string, name: string}[]>([])

// 原始树形数据
const originalTreeData = ref<Contact[]>([])

// 当前显示的树形数据
const treeData = ref<Contact[]>([])

// 已选联系人
const selectedContacts = ref<Contact[]>([])

// 树形配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算属性
const selectAll = computed({
  get() {
    const allNodes = getAllNodes(treeData.value)
    return allNodes.length > 0 && allNodes.every((node: Contact) => node.checked)
  },
  set(value: boolean) {
    const allNodes = getAllNodes(treeData.value)
    allNodes.forEach((node: Contact) => {
      // 只有用户才能被选中
      if (node.type === 'USER') {
        node.checked = value
      }
    })
    updateSelectedContacts()
  }
})

const isIndeterminate = computed(() => {
  const users = getUsers(treeData.value)
  const checkedCount = users.filter((user: Contact) => user.checked).length
  return checkedCount > 0 && checkedCount < users.length
})

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 初始化已选联系人
    selectedContacts.value = [...props.selectedContacts]
    // 初始化树形数据
    resetTreeData()
    // 同步树形数据的选中状态
    syncTreeSelection()
  }
})

// 监听弹窗内部状态
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 组件挂载时获取组织架构数据
onMounted(async () => {
  await fetchOrganizationTree()
})

// 获取组织架构树数据
const fetchOrganizationTree = async () => {
  try {
    const response = await OrganizationApi.getOrganizationTree()
    if (response.code === '0000') {
      // 转换后端数据结构为前端需要的结构
      originalTreeData.value = convertTreeData(response.data)
      resetTreeData()
    } else {
      ElMessage.error(response.message || '获取组织架构失败')
    }
  } catch (error) {
    ElMessage.error('获取组织架构失败')
  }
}

// 转换后端数据结构为前端需要的结构
const convertTreeData = (nodes: OrganizationTreeVO[]): Contact[] => {
  return nodes.map((node: OrganizationTreeVO) => {
    const contact: Contact = {
      id: String(node.id),
      name: node.name,
      type: node.type === 'USER' ? 'USER' : 'DEPT',
      checked: false
    }
    
    // 如果是用户，添加用户特有属性
    if (node.type === 'USER') {
      contact.avatar = node.avatar
    }
    
    // 如果有子节点，递归转换
    if (node.children && node.children.length > 0) {
      contact.children = convertTreeData(node.children)
    }
    
    return contact
  })
}

// 获取所有用户节点
const getUsers = (nodes: Contact[]): Contact[] => {
  const users: Contact[] = []
  const traverse = (nodeList: Contact[]) => {
    nodeList.forEach((node: Contact) => {
      if (node.type === 'USER') {
        users.push(node)
      } else if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(nodes)
  return users
}

// 同步树形数据的选中状态
const syncTreeSelection = () => {
  const users = getUsers(treeData.value)
  users.forEach((user: Contact) => {
    user.checked = selectedContacts.value.some(contact => contact.id === user.id)
  })
  
  // 展开被勾选的节点
  expandCheckedNodes(treeData.value)
}

// 获取所有节点
const getAllNodes = (nodes: Contact[]): Contact[] => {
  const allNodes: Contact[] = []
  const traverse = (nodeList: Contact[]) => {
    nodeList.forEach((node: Contact) => {
      allNodes.push(node)
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(nodes)
  return allNodes
}

// 更新已选联系人
const updateSelectedContacts = () => {
  const users = getUsers(treeData.value)
  selectedContacts.value = users.filter((user: Contact) => user.checked)
}

// 处理搜索
const handleSearch = () => {
  const keyword = searchKeyword.value.toLowerCase().trim()
  if (!keyword) {
    // 如果搜索关键词为空，显示所有数据
    resetTreeData()
    return
  }
  
  // 本地搜索用户数据
  localSearch(keyword)
}

// 本地搜索组织架构用户
const localSearch = (keyword: string) => {
  // 深拷贝原始数据
  const filteredData = JSON.parse(JSON.stringify(originalTreeData.value))
  
  // 递归过滤数据
  const filterNodes = (nodes: Contact[]): Contact[] => {
    return nodes.filter(node => {
      // 检查当前节点是否匹配
      const isMatch = node.name.toLowerCase().includes(keyword)
      
      // 如果当前节点匹配，保留它和所有子节点
      if (isMatch) {
        // 如果是部门节点，保留所有子节点
        // 如果是用户节点，只保留该节点
        if (node.type === 'DEPT' && node.children && node.children.length > 0) {
          // 保留部门的所有子节点
          node.children = JSON.parse(JSON.stringify(node.children))
        } else if (node.children && node.children.length > 0) {
          // 对于非部门节点，递归过滤子节点
          node.children = filterNodes(node.children)
        }
        return true
      }
      
      // 如果当前节点不匹配，检查子节点是否有匹配的
      if (node.children && node.children.length > 0) {
        const filteredChildren = filterNodes(node.children)
        if (filteredChildren.length > 0) {
          node.children = filteredChildren
          return true
        }
      }
      
      // 当前节点和子节点都不匹配，过滤掉
      return false
    })
  }
  
  
  treeData.value = filterNodes(filteredData)
  
  // 展开所有非叶子节点
  expandAllNonLeafNodes(treeData.value)
}

// 展开包含匹配结果的节点
const expandMatchingNodes = (nodes: Contact[], keyword: string) => {
  const expandedIds: string[] = []
  
  // 递归查找匹配的节点并收集需要展开的节点ID
  const collectExpandedIds = (nodeList: Contact[]) => {
    nodeList.forEach(node => {
      // 检查当前节点是否匹配
      const isMatch = node.name.toLowerCase().includes(keyword)
      
      // 如果当前节点匹配，或者有子节点且子节点中有匹配的，则展开当前节点
      if (isMatch || (node.children && node.children.length > 0)) {
        // 检查子节点是否有匹配的
        let hasMatchingChild = false
        if (node.children && node.children.length > 0) {
          // 检查直接子节点是否有匹配的
          hasMatchingChild = node.children.some(child =>
            child.name.toLowerCase().includes(keyword) ||
            (child.children && child.children.length > 0)
          )
          
          // 递归处理子节点
          collectExpandedIds(node.children)
        }
        
        // 如果当前节点匹配或有匹配的子节点，则展开它
        if (isMatch || hasMatchingChild) {
          expandedIds.push(node.id)
        }
      }
    })
  }
  
  collectExpandedIds(nodes)
  
  // 更新展开的节点
  expandedKeys.value = [...new Set(expandedIds)] // 去重
}

// 展开所有非叶子节点
const expandAllNonLeafNodes = (nodes: Contact[]) => {
  const expandedIds: string[] = []
  
  // 递归收集所有非叶子节点的ID
  const collectNonLeafNodeIds = (nodeList: Contact[]) => {
    nodeList.forEach(node => {
      // 如果节点有子节点，则展开它
      if (node.children && node.children.length > 0) {
        expandedIds.push(node.id)
        // 递归处理子节点
        collectNonLeafNodeIds(node.children)
      }
    })
  }
  
  collectNonLeafNodeIds(nodes)
  
  // 更新展开的节点
  expandedKeys.value = expandedIds
}

// 展开被勾选的节点
const expandCheckedNodes = (nodes: Contact[]) => {
  const expandedIds: string[] = []
  
  // 递归收集所有被勾选节点的ID及其父节点ID
  const collectCheckedNodeIds = (nodeList: Contact[]) => {
    nodeList.forEach(node => {
      // 如果节点被勾选，或者有子节点被勾选，则展开它
      if (node.checked || (node.children && node.children.some(child => child.checked))) {
        expandedIds.push(node.id)
      }
      
      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        collectCheckedNodeIds(node.children)
      }
    })
  }
  
  collectCheckedNodeIds(nodes)
  
  // 更新展开的节点
  expandedKeys.value = expandedIds
}

// 高亮搜索关键词
const highlightKeyword = (text: string, keyword: string) => {
  if (!keyword) return text
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span class="highlight">$1</span>')
}

// 重置树形数据到原始状态
const resetTreeData = () => {
  treeData.value = JSON.parse(JSON.stringify(originalTreeData.value))
}

// 返回上级
const goBack = () => {
  if (navigationHistory.value.length > 0) {
    // 弹出最后一个历史记录
    const previous = navigationHistory.value.pop()
    if (previous) {
      currentPath.value = previous.name
      // 重新加载数据
      resetTreeData()
    }
  }
}

// 是否可以返回
const canGoBack = computed(() => {
  return currentPath.value !== '组织架构'
})

// 处理节点点击
const handleNodeClick = (data: Contact) => {
  if (data.type === 'DEPT') {
    // 记录当前路径到历史记录
    navigationHistory.value.push({id: data.id, name: currentPath.value})
    // 更新当前路径
    currentPath.value = data.name
    // 展开/收起部门
    const node = treeRef.value.getNode(data.id)
    if (node.expanded) {
      node.collapse()
    } else {
      node.expand()
    }
  }
}

// 递归设置节点及其子节点的选中状态
const setNodeChecked = (node: Contact, checked: boolean) => {
  // 设置当前节点的选中状态
  node.checked = checked
  
  // 如果有子节点，递归设置子节点的选中状态
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      // 只有用户节点或部门节点都可以被选中
      setNodeChecked(child, checked)
    })
  }
}

// 更新父节点的选中状态
const updateParentNodeChecked = (nodeId: string, tree: Contact[]) => {
  // 查找父节点
  const findParent = (targetNodeId: string, nodeList: Contact[]): Contact | null => {
    for (const n of nodeList) {
      if (n.children && n.children.some(child => child.id === targetNodeId)) {
        return n
      }
      if (n.children && n.children.length > 0) {
        const parent = findParent(targetNodeId, n.children)
        if (parent) {
          return parent
        }
      }
    }
    return null
  }
  
  // 更新父节点的选中状态
  const updateParent = (targetNodeId: string) => {
    const parent = findParent(targetNodeId, tree)
    if (parent) {
      // 检查是否所有子节点都被选中
      const allChildrenChecked = parent.children?.every(child => child.checked) || false
      
      // 如果所有子节点都被选中，则父节点也被选中
      // 否则父节点不被选中（包括部分选中和全不选中）
      parent.checked = allChildrenChecked
      
      // 递归更新祖父节点
      updateParent(parent.id)
    }
  }
  
  updateParent(nodeId)
}

// 处理单个项目选择
const handleItemSelect = (data: Contact, checked: any) => {
  // 递归设置节点及其子节点的选中状态
  setNodeChecked(data, Boolean(checked))
  // 更新父节点的选中状态
  updateParentNodeChecked(data.id, treeData.value)
  updateSelectedContacts()
}

// 处理全选
const handleSelectAll = (checked: any) => {
  const users = getUsers(treeData.value)
  users.forEach((user: Contact) => {
    user.checked = Boolean(checked)
  })
  updateSelectedContacts()
}

// 移除联系人
const removeContact = (contact: Contact) => {
  const index = selectedContacts.value.findIndex(c => c.id === contact.id)
  if (index > -1) {
    selectedContacts.value.splice(index, 1)
    // 同步更新树形数据
    const users = getUsers(treeData.value)
    const user = users.find(u => u.id === contact.id)
    if (user) {
      user.checked = false
    }
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  searchKeyword.value = ''
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', selectedContacts.value)
  visible.value = false
  searchKeyword.value = ''
  ElMessage.success('人员选择完成')
}
</script>

<style scoped lang="scss">
.contact-selector {
  .search-bar {
    margin-bottom: 16px;
  }

  .navigation-path {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 0;
    border-bottom: 1px solid #e4e7ed;

    .current-path {
      margin-left: 8px;
      color: #606266;
    }
  }

  .selector-content {
    display: flex;
    gap: 20px;
    height: 400px;
  }

  .organization-tree {
    flex: 1;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .tree-header {
      padding: 12px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #f5f7fa;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search-status {
        font-size: 12px;
        color: #909399;
        font-style: italic;
      }
    }

    .tree-container {
      flex: 1;
      overflow-y: auto;
      padding: 8px;

      .tree-node {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 0;

        .node-label {
          flex: 1;
        }

        .user-avatar {
          margin-left: auto;
        }
      }
    }
  }

  .selected-contacts {
    width: 200px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .selected-header {
      padding: 12px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #f5f7fa;
      font-size: 14px;
      color: #606266;
    }

    .selected-list {
      flex: 1;
      padding: 12px;
      overflow-y: auto;

      .contact-tag {
        margin: 4px;
        background-color: #409eff;
        border-color: #409eff;
        color: #fff;

        &:hover {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }

      .empty-tip {
        color: #909399;
        text-align: center;
        padding: 20px 0;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

.highlight {
  background-color: #ffd04b;
  color: #000;
  padding: 1px 2px;
  border-radius: 2px;
}
</style>
